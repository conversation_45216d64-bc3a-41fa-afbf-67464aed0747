/**
 * Skipped minification because the original files appears to be already minified.
 * Original file: /npm/cube-solver@2.4.1/dist/bundle.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).cubeSolver=t()}(this,function(){"use strict";function t(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function l(r){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?t(Object(o),!0).forEach(function(e){var t,n;t=r,e=o[n=e],n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(o)):t(Object(o)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(o,e))})}return r}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function e(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function n(e,t){return(n=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function i(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function a(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=o(n);return i(this,r?(e=o(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments))}}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(u)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Map"===(n="Object"===n&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function c(e,t){return Math.floor(Math.random()*(t-e+1))+e}function h(e,t){var n=e.slice(0);n[t[0]]=e[t[t.length-1]];for(var r=1;r<t.length;r+=1)n[t[r]]=e[t[r-1]];return n}function v(e,t,n){for(var r=3<arguments.length&&void 0!==arguments[3]&&arguments[3],o=H(t.length),i=Math.floor(e/o),a=e%o,u=[],f=0;f<n;f+=1)u.push(-1);for(var l=1;l<t.length;l+=1)for(var s=a%(l+1),a=Math.floor(a/(l+1));0<s;)Q(t,0,l),--s;var c=t.length-1;if(r)for(var h=0;h<n;h+=1){var v=K(n-1-h,c+1);0<=i-v&&(u[h]=t[t.length-1-c],i-=v,--c)}else for(var d=n-1;0<=d;--d){var g=K(d,c+1);0<=i-g&&(u[d]=t[c],i-=g,--c)}return u}function d(e){return/^([FRUBLDfrubldxyzMSE][2']?\s*)+$/.test(e)}function g(o){o=o.reduce(function(e,t){var n=t.charAt(0),r=ee[t.charAt(1)];if(te[n]){for(var o=0;o<=r;o+=1)e=e.concat(te[n]);return e}return e.concat(t)},[]);for(var i=[],a=[],e=o.length-1;0<=e;--e)!function(e){var t=o[e].charAt(0),n=ee[o[e].charAt(1)];if("xyz".includes(t)){a.unshift(o[e]);for(var r=0;r<=n;r+=1)i=i.map(function(e){return ne[t]["FRUBLD".indexOf(e[0])]+e.charAt(1)})}else i.unshift(o[e])}(e);return[i,a]}function m(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];if(!d(e))throw new Error("Invalid algorithm provided to algorithm parser");var n=[],e=(r=s(g(e.match(/[FRUBLDfrubldxyzMSE][2']?/g)),2))[0],r=r[1];return e.forEach(function(e){var t="FRUBLD".indexOf(e.charAt(0)),e=ee[e.charAt(1)];n.push(3*t+e)}),t?[n,r]:n}function b(e){var t="";return e.forEach(function(e){switch(t+=" ",t+="FRUBLD".charAt(Math.floor(e/3)),e%3){case 1:t+="2";break;case 2:t+="'"}}),t.trim()}function p(e,t){var n=Math.floor(t/3),r=ge[n],o=t%3,i=pe(e,t);if((0===n||3===n)&&o%2==0)for(var a=0;a<4;a+=1)i[r[a]]=(i[r[a]]+1)%2;return i}function y(e,t){var n=Math.floor(t/3),r=me[n],o=t%3,i=ye(e,t);if(2!==n&&5!==n&&o%2==0)for(var a=0;a<4;a+=1)i[r[a]]=(i[r[a]]+(a+1)%2+1)%3;return i}function x(t){return new Te({name:t.name,moves:t.moves,defaultIndex:Z([0,1,2,3,4,5,6,7],t.affected,t.reversed),size:t.size||H(8)/H(8-t.affected.length),getVector:function(e){return v(e,t.affected.slice(),8,t.reversed)},cubieMove:ye,getIndex:function(e){return Z(e,t.affected,t.reversed)}})}function T(t){return new Te({name:t.name,moves:t.moves,defaultIndex:Z([0,1,2,3,4,5,6,7,8,9,10,11],t.affected,t.reversed),size:t.size||H(12)/H(12-t.affected.length),getVector:function(e){return v(e,t.affected.slice(),12,t.reversed)},cubieMove:pe,getIndex:function(e){return Z(e,t.affected,t.reversed)}})}function w(n,r,o){for(var i=[],e=Math.pow(o,r-1),t=Math.pow(o,r-n.length-1),a=0;a<e&&i.length<t;a+=1)!function(e){var t=X(e,r,o);n.every(function(e){return 0===t[e]})&&i.push(e)}(a);return i}function O(e){return new Te({name:e.name,size:2048,solvedIndexes:w(e.affected,12,2),getVector:function(e){return X(e,12,2)},cubieMove:p,getIndex:function(e){return W(e,2)}})}function M(e){return new Te({name:e.name,size:2187,solvedIndexes:w(e.affected,8,3),getVector:function(e){return X(e,8,3)},cubieMove:y,getIndex:function(e){return W(e,3)}})}function P(e){return Me.solve({scramble:e})}function z(e){return Pe.solve({scramble:e})}function E(e){return ze.solve({scramble:e})}function D(e){return Ae.solve({scramble:e})}function I(e,t){for(var n,r,o,i,a=2<arguments.length&&void 0!==arguments[2]?arguments[2]:e,u=3<arguments.length&&void 0!==arguments[3]?arguments[3]:t,f=4<arguments.length&&void 0!==arguments[4]&&arguments[4],l=5<arguments.length&&void 0!==arguments[5]&&arguments[5];n=Ue(a,2,12),r=Fe(e,12),f&&(r=ke(r)),o=Ue(u,3,8),i=Fe(t,8),l&&(i=ke(i)),Y(r)!==Y(i););return Re(n,r,o,i)}function j(){return I([re,oe,ie,ae],[],[],[se,ce,he,ve],!1,!0)}function R(){for(var e,t,n,r;e=X(c(0,2048),12,2),n=X(c(0,2187),8,3),t=v(c(0,479001600),[0,1,2,3,4,5,6,7,8,9,10,11],12),r=v(c(0,40320),[0,1,2,3,4,5,6,7],8),Y(t)!==Y(r););return Re(e,t,n,r)}function A(){return I([re,oe,ie,ae,ue,fe],[se,ce,he,ve])}function S(){return I([],[0,1,2,3,4,5,6,7])}function U(){return I([0,1,2,3,4,5,6,7,8,9,10,11],[])}function F(){return I(Be,[],Be,[],!1,!0)}function k(){return I([re,oe,ie,ae,le],[se,ce,he,ve,de])}function B(){return I([re,oe,ie,ae],[se,ce,he,ve],[],[])}function L(){return I(Le,Ve,[],Ve)}function V(){return I([re,oe,ie,ae,le],Ce,[],Ce)}var C,_,$,q,G=[],H=function e(t){return 0===t||1===t?1:(0<G[t]||(G[t]=e(t-1)*t),G[t])},J=[],K=function(e,t){if(e<t)return 0;for(;e>=J.length;){for(var n=J.length,r=[],o=r[0]=1,i=n-1;o<n;o+=1)r[o]=J[i][o-1]+J[i][o];r[n]=1,J.push(r)}return J[e][t]},N=function(e,t,n){for(var r=e[t],o=t;o<n;o+=1)e[o]=e[o+1];e[n]=r},Q=function(e,t,n){for(var r=e[n],o=n;t<o;--o)e[o]=e[o-1];e[t]=r},W=function(e,t){for(var n=0,r=0;r<e.length-1;r+=1)n=t*n+e[r];return n},X=function(e,t,n){for(var r=[],o=0,i=t-2;0<=i;--i){var a=e%n;e=Math.floor(e/n),o+=r[i]=a}return r[t-1]=(n-o%n)%n,r},Y=function(e){for(var t=0,n=e.length-1;0<n;--n)for(var r=n-1;0<=r;--r)e[r]>e[n]&&(t+=1);return t%2},Z=function(e,t){var n=e.length-1,r=0,o=1,i=[];if(2<arguments.length&&void 0!==arguments[2]&&arguments[2])for(var a=e.length-1;0<=a;--a)0<=t.indexOf(e[a])&&(n=Math.min(n,e[a]),r+=K(e.length-1-a,o),i.unshift(e[a]),o+=1);else for(var u=0;u<e.length;u+=1)0<=t.indexOf(e[u])&&(n=Math.min(n,e[u]),r+=K(u,o),i.push(e[u]),o+=1);for(var f=0,l=i.length-1;0<l;--l){for(var s=0;i[l]!==t[l];)N(i,0,l),s+=1;f=(l+1)*f+s}return H(t.length)*r+f},ee={"":0,2:1,"'":2},te={f:["z","B"],r:["x","L"],u:["y","D"],b:["z'","F"],l:["x'","R"],d:["y'","U"],M:["x'","R","L'"],S:["z","F'","B"],E:["y'","U","D'"]},ne={x:"DRFULB",y:"RBULFD",z:"FULBDR"},re=0,oe=1,ie=2,ae=3,ue=5,fe=7,le=8,se=0,ce=1,he=2,ve=3,de=4,ge=[[1,8,5,9],[0,11,4,8],[1,2,3,0],[3,10,7,11],[2,9,6,10],[5,4,7,6]],me=[[1,0,4,5],[0,3,7,4],[0,1,2,3],[3,2,6,7],[2,1,5,6],[5,4,7,6]],be=function(e,t,n){for(var r=e,o=n[Math.floor(t/3)],i=t%3,a=0;a<=i;a+=1)r=h(r,o);return r},pe=function(e,t){return be(e,t,ge)},ye=function(e,t){return be(e,t,me)},xe=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17],Te=function(){function t(n){var e,r,o,i,a=this;u(this,t),this.name=n.name,this.size=n.size,this.defaultIndex=n.defaultIndex||0,this.solvedIndexes=n.solvedIndexes||[this.defaultIndex],n.doMove&&(this.doMove=function(e,t){return n.doMove(a.table,e,t)}),n.table?this.table=n.table:(r=n.getVector,o=n.cubieMove,i=n.getIndex,e=function(e,t){e=r(e),e=o(e,t);return i(e)},this.createMoveTable(n.size,e,n.moves))}return e(t,[{key:"doMove",value:function(e,t){return this.table[e][t]}},{key:"createMoveTable",value:function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:xe;this.table=[];for(var r=0;r<e;r+=1)this.table.push([]);for(var o=0;o<e;o+=1)for(var i=0;i<n.length;i+=1){var a,u,f=n[i];this.table[o][f]||(a=t(o,f),u=f-f%3*2+2,this.table[o][f]=a,this.table[a][u]=o)}}}]),t}(),we=function(){function n(e,t){u(this,n),this.computePruningTable(e,t)}return e(n,[{key:"setPruningValue",value:function(e,t){this.table[e>>3]^=(15^t)<<((7&e)<<2)}},{key:"getPruningValue",value:function(e){return this.table[e>>3]>>((7&e)<<2)&15}},{key:"computePruningTable",value:function(e,t){var n=e.reduce(function(e,t){return e*t.size},1);this.table=[];for(var r=0;r<n+7>>3;r+=1)this.table.push(-1);for(var o=0,i=0,a=[1],u=1;u<e.length;u+=1)a.push(e[u-1].size*a[u-1]);for(var f,l,s,c=(f=e.map(function(e){return e.solvedIndexes}),l=[],s=f.length-1,function e(t,n){for(var r=0;r<f[n].length;r+=1){var o=t.slice(0);o.push(f[n][r]),n===s?l.push(o):e(o,n+1)}}([],0),l),h=0;h<c.length;h+=1){for(var v=0,d=0;d<c[h].length;d+=1)v+=a[d]*c[h][d];this.setPruningValue(v,0),i+=1}for(;i!==n;){var g=n/2<i,m=g?15:o,b=g?o:15;o+=1;for(var p=0;p<n;p+=1)if(this.getPruningValue(p)===m)for(var y=0;y<t.length;y+=1){for(var x=t[y],T=p,w=0,O=a.length-1;0<=O;--O)w+=a[O]*e[O].doMove(Math.floor(T/a[O]),x),T%=a[O];if(this.getPruningValue(w)===b){if(i+=1,g){this.setPruningValue(p,o);break}this.setPruningValue(w,o)}}}}}]),n}(),Oe=function(){function n(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:xe;u(this,n),this.createTables=e,this.moves=t}return e(n,[{key:"initialize",value:function(){var e,t,r=this;this.initialized||(this.initialized=!0,e=(t=this.createTables()).moveTables,t=t.pruningTables,this.moveTables=e,this.pruningTables=[],t.forEach(function(e){var t=e.map(function(e){return r.moveTables.map(function(e){return e.name}).indexOf(e)});t.sort(function(e,t){return r.moveTables[e].size-r.moveTables[t].size});var n=[];t.forEach(function(e){return n.push(r.moveTables[e])});e=new we(n,r.moves);r.pruningTables.push({pruningTable:e,moveTableIndexes:t})}))}},{key:"handleSolution",value:function(e,t){return{solution:e,indexes:t}}},{key:"search",value:function(e,t,n,r){for(var o=0,i=0;i<this.pruningTables.length;i+=1){for(var a=e[this.pruningTables[i].moveTableIndexes[0]],u=1,f=1;f<this.pruningTables[i].moveTableIndexes.length;f+=1)u*=this.moveTables[this.pruningTables[i].moveTableIndexes[f-1]].size,a+=e[this.pruningTables[i].moveTableIndexes[f]]*u;var l=this.pruningTables[i].pruningTable.getPruningValue(a);if(t<l)return!1;o<l&&(o=l)}if(0===o)return this.handleSolution(r,e);if(0<t)for(var s=0;s<this.moves.length;s+=1){var c=this.moves[s];if(Math.floor(c/3)!==Math.floor(n/3)&&Math.floor(c/3)!==Math.floor(n/3)-3){for(var h=[],v=0;v<e.length;v+=1)h.push(this.moveTables[v].doMove(e[v],c));var d=this.search(h,t-1,c,r.concat([c]));if(d)return d}}return!1}},{key:"solve",value:function(e){var n=this;this.initialize(),this.settings=l({maxDepth:22,lastMove:null,format:!0},e);var t,r=this.settings.indexes||[];if(this.settings.scramble){var o=s(m(this.settings.scramble,!0),2),e=o[0],o=o[1];0<o.length&&(t=function(e){if(!d(e))throw new Error("Invalid algorithm provided to algorithm parser");return e.match(/[FRUBLDfrubldxyzMSE][2']?/g).reverse().map(function(e){var t=e.charAt(0),e=ee[e.charAt(1)],e=e-e%3*2+2;return 1==e?"".concat(t,"2"):2==e?"".concat(t,"'"):t}).join(" ")}(o.join(" ")));for(var i=0;i<this.moveTables.length;i+=1)r.push(this.moveTables[i].defaultIndex);e.forEach(function(e){for(var t=0;t<r.length;t+=1)r[t]=n.moveTables[t].doMove(r[t],e)})}for(var a=0;a<=this.settings.maxDepth;a+=1){var u=this.search(r,a,this.settings.lastMove,[]);if(u){if(this.settings.format){var f=b(u.solution);return t?b(m("".concat(t," ").concat(f))):f}return u}}return!1}}]),n}(),Me=new Oe(function(){return{moveTables:[T({name:"EdgePermutation",affected:[4,5,6,7]}),O({name:"EdgeOrientation",affected:[4,5,6,7]})],pruningTables:[["EdgePermutation"],["EdgeOrientation"]]}}),Pe=new Oe(function(){return{moveTables:[O({name:"EdgeOrientation",affected:[0,1,2,3,4,5,6,7,8,9,10,11]}),T({name:"EdgePermutation",affected:[5,7]})],pruningTables:[["EdgeOrientation"],["EdgePermutation"]]}}),ze=new Oe(function(){return{moveTables:[O({name:"EdgeOrientation",affected:[6,9,10]}),M({name:"CornerOrientation",affected:[5,6]}),T({name:"EdgePermutation",affected:[6,9,10]}),x({name:"CornerPermutation",affected:[5,6]})],pruningTables:[["EdgeOrientation","CornerPermutation"],["CornerOrientation","CornerPermutation"],["EdgePermutation","CornerPermutation"]]}}),Ee=[1,10,4,13,6,7,8,15,16,17],De=new Oe(function(){q=[];for(var e=0;e<336;e+=1){q.push([]);for(var t=0;t<336;t+=1)q[e][t]=function(e,t){for(var n=v(e,[0,1,2],12),r=v(t,[3,4,5],12),o=0;o<8;o+=1)if(-1!==n[o]){if(-1!==r[o])return-1;r[o]=n[o]}return Z(r,[0,1,2,3,4,5])}(e,t)}return{moveTables:[new Te({name:"slicePermutation",size:24,table:$.table}),C,_,T({name:"URToDF",size:20160,moves:Ee,affected:[0,1,2,3,4,5]})],pruningTables:[["slicePermutation","parity","URFToDLF"],["slicePermutation","parity","URToDF"]]}},Ee),Ie=new(function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&n(e,t)}(i,Oe);var o=a(i);function i(){var e;u(this,i);for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return(e=o.call.apply(o,[this].concat(n))).maxDepth=22,e.solution=null,e}return e(i,[{key:"handleSolution",value:function(e,t){var n=e.slice(-1)[0];if(n%2==0&&6===Math.floor(n/3)&&15===Math.floor(n/3))return!1;n=De.solve({indexes:[t[3],t[4],t[5],q[t[6]][t[7]]],maxDepth:this.maxDepth-e.length,lastMove:n,format:!1});if(n){if(this.solution=e.concat(n.solution),this.maxDepth<=this.settings.maxDepth)return{solution:this.solution,indexes:t};this.maxDepth=this.solution.length-1}return!1}}]),i}())(function(){return C=new Te({name:"parity",size:2,table:[[1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1],[0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0]]}),_=x({name:"URFToDLF",affected:[0,1,2,3,4,5]}),$=T({name:"slice",affected:[8,9,10,11],reversed:!0}),De.initialize(),{moveTables:[new Te({name:"slicePosition",size:495,table:$.table,doMove:function(e,t,n){return Math.floor(e[24*t][n]/24)}}),M({name:"twist",affected:[0,1,2,3,4,5,6,7]}),O({name:"flip",affected:[0,1,2,3,4,5,6,7,8,9,10,11]}),$,C,_,T({name:"URToUL",affected:[0,1,2]}),T({name:"UBToDF",affected:[3,4,5]})],pruningTables:[["slicePosition","flip"],["slicePosition","twist"]]}}),je=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:22;return Array.isArray(e)?Ie.solve({indexes:e,maxDepth:t}):Ie.solve({scramble:e,maxDepth:t})},Re=function(e,t,n,r){return je([Math.floor(Z(t,[8,9,10,11],!0)/24),W(n,3),W(e,2),Z(t,[8,9,10,11],!0),Y(r),Z(r,[0,1,2,3,4,5]),Z(t,[0,1,2]),Z(t,[3,4,5])])},Ae=new Oe(function(){return{moveTables:[T({name:"EdgePermutation",affected:[4,5,6,7,9]}),O({name:"EdgeOrientation",affected:[4,5,6,7,9]}),x({name:"CornerPermutation",affected:[5]}),M({name:"CornerOrientation",affected:[5]})],pruningTables:[["EdgePermutation","CornerPermutation"],["EdgeOrientation","CornerOrientation"]]}}),Se=[0,1,2,3],Ue=function(e,t,n){var r=X(c(0,Math.pow(t,e.length-1)),e.length,t),o=Array(n).fill(0);return e.forEach(function(e,t){o[e]=r[t]}),o},Fe=function(e,t){var n=v(c(0,H(e.length)),e.slice(0),e.length),r=[0,1,2,3,4,5,6,7,8,9,10,11].slice(0,t);return e.forEach(function(e,t){r[e]=n[t]}),r},ke=function(e,t){t=t||c(0,4);for(var n=0;n<t;n+=1)e=h(e,Se);return e},Be=[re,oe,ie,ae,ue,fe],Le=[re,oe,ie,ae],Ve=[se,ce,he,ve],Ce=[se,ce,he,ve,de];return{solve:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"kociemba",n={kociemba:je,cross:P,eoline:z,fb:E,xcross:D};if(n[t])return n[t](e);throw new Error("Specified solver does not exist.")},scramble:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"3x3",t={"3x3":R,"2gll":j,cmll:A,corners:S,edges:U,lse:F,lsll:k,pll:B,zbll:L,zzls:V};if(t[e])return t[e]();throw new Error("Specified scrambler does not exist.")},initialize:function(e){var t={cross:Me,eoline:Pe,fb:ze,xcross:Ae};if("kociemba"===e)Ie.initialize(),De.initialize();else{if(!t[e])throw new Error("Specified solver does not exist.");t[e].initialize()}}}});
