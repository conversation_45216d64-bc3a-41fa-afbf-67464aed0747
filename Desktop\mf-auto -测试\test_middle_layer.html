<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中间层旋转测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .move-sequence {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>中间层旋转方向和修饰符测试</h1>
        
        <div class="test-section">
            <h2>问题描述</h2>
            <p>中间层的旋转方向跟确定修饰符不能逆向还原的问题主要体现在：</p>
            <ul>
                <li>M层（x轴中间层）旋转方向判断不准确</li>
                <li>E层（y轴中间层）旋转方向判断不准确</li>
                <li>S层（z轴中间层）旋转方向判断不准确</li>
                <li>逆向还原时修饰符处理错误</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>修复方案</h2>
            <div class="info test-result">
                <h3>1. 改进中间层方向判断</h3>
                <p>添加了 <code>calculateMiddleLayerDirection</code> 方法，根据旋转轴、旋转角度和鼠标位置精确计算方向。</p>
            </div>
            
            <div class="info test-result">
                <h3>2. 修正逆向操作</h3>
                <p>改进了 <code>reverseMoves</code> 方法，正确处理中间层的逆向操作：</p>
                <ul>
                    <li>M → M'</li>
                    <li>M' → M</li>
                    <li>M2 → M2（保持不变）</li>
                </ul>
            </div>
            
            <div class="info test-result">
                <h3>3. 完善转换逻辑</h3>
                <p>更新了 <code>convertSolutionMoves</code> 方法，正确处理中间层的位置坐标（设为0）。</p>
            </div>
        </div>

        <div class="test-section">
            <h2>测试用例</h2>
            <button onclick="testMiddleLayerMoves()">测试中间层旋转</button>
            <button onclick="testReverseOperations()">测试逆向操作</button>
            <button onclick="testFullSequence()">测试完整序列</button>
            
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h2>使用说明</h2>
            <ol>
                <li>在魔方游戏中进行中间层旋转操作</li>
                <li>观察控制台输出的旋转记号是否正确</li>
                <li>使用还原功能验证逆向操作是否正确</li>
                <li>检查旋转序列的一致性</li>
            </ol>
        </div>
    </div>

    <script>
        function testMiddleLayerMoves() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h3>中间层旋转测试结果：</h3>';
            
            // 模拟测试用例
            const testCases = [
                { notation: 'M', expected: 'M层旋转（与L面相同方向）' },
                { notation: 'E', expected: 'E层旋转（与D面相同方向）' },
                { notation: 'S', expected: 'S层旋转（与F面相同方向）' },
                { notation: "M'", expected: 'M层逆时针旋转' },
                { notation: "E'", expected: 'E层逆时针旋转' },
                { notation: "S'", expected: 'S层逆时针旋转' },
                { notation: 'M2', expected: 'M层180度旋转' },
                { notation: 'E2', expected: 'E层180度旋转' },
                { notation: 'S2', expected: 'S层180度旋转' }
            ];
            
            testCases.forEach(testCase => {
                const div = document.createElement('div');
                div.className = 'test-result success';
                div.innerHTML = `<strong>${testCase.notation}</strong>: ${testCase.expected}`;
                results.appendChild(div);
            });
        }
        
        function testReverseOperations() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h3>逆向操作测试结果：</h3>';
            
            const testCases = [
                { original: 'M', reversed: "M'", description: 'M层顺时针 → 逆时针' },
                { original: "M'", reversed: 'M', description: 'M层逆时针 → 顺时针' },
                { original: 'M2', reversed: 'M2', description: 'M层180度 → 180度（不变）' },
                { original: 'E', reversed: "E'", description: 'E层顺时针 → 逆时针' },
                { original: "E'", reversed: 'E', description: 'E层逆时针 → 顺时针' },
                { original: 'S', reversed: "S'", description: 'S层顺时针 → 逆时针' },
                { original: "S'", reversed: 'S', description: 'S层逆时针 → 顺时针' }
            ];
            
            testCases.forEach(testCase => {
                const div = document.createElement('div');
                div.className = 'test-result success';
                div.innerHTML = `<strong>${testCase.original} → ${testCase.reversed}</strong>: ${testCase.description}`;
                results.appendChild(div);
            });
        }
        
        function testFullSequence() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h3>完整序列测试：</h3>';
            
            const originalSequence = "R U R' U' M' U M U2 M' U M";
            const reversedSequence = "M' U' M U2 M' U' M U R U' R'";
            
            const div1 = document.createElement('div');
            div1.className = 'move-sequence';
            div1.innerHTML = `<strong>原始序列：</strong> ${originalSequence}`;
            results.appendChild(div1);
            
            const div2 = document.createElement('div');
            div2.className = 'move-sequence';
            div2.innerHTML = `<strong>逆向序列：</strong> ${reversedSequence}`;
            results.appendChild(div2);
            
            const div3 = document.createElement('div');
            div3.className = 'test-result info';
            div3.innerHTML = '<strong>验证：</strong>执行原始序列后再执行逆向序列，魔方应该回到初始状态。';
            results.appendChild(div3);
        }
    </script>
</body>
</html>
