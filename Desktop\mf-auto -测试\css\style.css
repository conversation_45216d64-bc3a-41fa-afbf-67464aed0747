
*, *:before, *:after {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  box-sizing: border-box;
  cursor: inherit;
  margin: 0;
  padding: 0;
  outline: none;
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  font-style: inherit;
  text-transform: uppercase;
}
*:focus {
  outline: none;
}

html {
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  overflow: hidden;
  height: 100%;
}

body {
  font-family: "BungeeFont", sans-serif;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  cursor: default;
  overflow: hidden;
  height: 100%;
  font-size: 5rem;
}

.icon {
  display: inline-block;
  font-size: inherit;
  overflow: visible;
  vertical-align: -0.125em;
  preserveAspectRatio: none;
}

.range {
  position: relative;
  width: 14em;
  z-index: 1;
  opacity: 0;
}
.range:not(:last-child) {
  margin-bottom: 2em;
}
.range__label {
  position: relative;
  font-size: 0.9em;
  line-height: 0.75em;
  padding-bottom: 0.5em;
  z-index: 2;
}
.range__track {
  position: relative;
  height: 1em;
  margin-left: 0.5em;
  margin-right: 0.5em;
  z-index: 3;
}
.range__track-line {
  position: absolute;
  background: rgba(0, 0, 0, 0.2);
  height: 2px;
  top: 50%;
  margin-top: -1px;
  left: -0.5em;
  right: -0.5em;
  transform-origin: left center;
}
.range__handle {
  position: absolute;
  width: 0;
  height: 0;
  top: 50%;
  left: 0;
  cursor: pointer;
  z-index: 1;
}
.range__handle div {
  transition: background 500ms ease;
  position: absolute;
  left: 0;
  top: 0;
  width: 0.9em;
  height: 0.9em;
  border-radius: 0.2em;
  margin-left: -0.45em;
  margin-top: -0.45em;
  background: #41aac8;
  border-bottom: 2px solid rgba(0, 0, 0, 0.2);
}
.range.is-active .range__handle div {
  transform: scale(1.25);
}
.range__handle:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 3em;
  height: 3em;
  margin-left: -1.5em;
  margin-top: -1.5em;
}
.range__list {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  position: relative;
  padding-top: 0.5em;
  font-size: 0.55em;
  color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.stats {
  position: relative;
  width: 14em;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  opacity: 0;
}
.stats:not(:last-child) {
  margin-bottom: 1.5em;
}
.stats i {
  display: inline-block;
  color: rgba(0, 0, 0, 0.5);
  font-size: 0.9em;
}
.stats b {
  display: inline-block;
  font-size: 0.9em;
}

.text {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  line-height: 0.75;
  perspective: 100rem;
  opacity: 0;
}
.text i {
  display: inline-block;
  opacity: 0;
  white-space: pre-wrap;
}
.text--title {
  bottom: 75%;
  font-size: 4.4em;
  height: 1.2em;
}
.text--title span {
  display: block;
}
.text--title span:first-child {
  font-size: 0.5em;
  margin-bottom: 0.2em;
}
.text--note {
  top: 87%;
  font-size: 1em;
}
.text--timer {
  bottom: 78%;
  font-size: 3.5em;
  line-height: 1;
}
.text--complete, .text--best-time {
  font-size: 1.5em;
  top: 83%;
  line-height: 1em;
}
.text--moves {
  bottom: 5%;
  font-size: 1em;
  line-height: 1.2em;
  color: rgba(255, 255, 255, 0.8);
  max-width: 80%;
  word-wrap: break-word;
  text-align: center;
}
.moves-sequence {
  font-family: monospace;
  font-weight: bold;
  color: #fff;
}

.btn {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: transparent;
  border-radius: 0;
  border-width: 0;
  position: absolute;
  pointer-events: none;
  font-size: 1.2em;
  color: rgba(0, 0, 0, 0.25);
  opacity: 0;
}
.btn:after {
  position: absolute;
  content: "";
  width: 3em;
  height: 3em;
  left: 50%;
  top: 50%;
  margin-left: -1.5em;
  margin-top: -1.5em;
  border-radius: 100%;
}
.btn--bl {
  bottom: 0.8em;
  left: 0.8em;
}
.btn--br {
  bottom: 0.8em;
  right: 0.8em;
}
.btn--bc {
  bottom: 0.8em;
  left: calc(50% - 0.5em);
}
.btn--pwa {
  transition: color 500ms ease;
  color: inherit;
  height: 1em;
}
.btn--pwa svg {
  font-size: 0.6em;
  margin: 0.35em 0;
}
.btn svg {
  display: block;
}

.ui {
  pointer-events: none;
  color: #070d15;
}
.ui, .ui__background, .ui__game, .ui__texts, .ui__prefs, .ui__stats, .ui__buttons {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.ui__background {
  z-index: 1;
  transition: background 500ms ease;
  background: #d1d5db;
}
.ui__background:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-image: linear-gradient(to bottom, white 50%, rgba(255, 255, 255, 0) 100%);
}
.ui__game {
  pointer-events: all;
  z-index: 2;
}
.ui__game canvas {
  display: block;
  width: 100%;
  height: 100%;
}
.ui__texts {
  z-index: 3;
}
.ui__prefs, .ui__stats {
  display: flex;
  flex-flow: column nowrap;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  z-index: 4;
}
.ui__buttons {
  z-index: 5;
}
.ui__notification {
  transition: transform 500ms ease, opacity 500ms ease;
  font-family: sans-serif;
  position: absolute;
  left: 50%;
  bottom: 0.6em;
  padding: 0.6em;
  margin-left: -9.4em;
  width: 18.8em;
  z-index: 6;
  background: rgba(17, 17, 17, 0.9);
  border-radius: 0.8em;
  display: flex;
  align-items: center;
  flex-flow: row nowrap;
  color: #fff;
  user-select: none;
  opacity: 0;
  pointer-events: none;
  transform: translateY(100%);
}
.ui__notification.is-active {
  opacity: 1;
  pointer-events: all;
  transform: none;
}
.ui__notification * {
  text-transform: none;
}
.ui__notification-icon {
  background-size: 100% 100%;
  left: 0.6em;
  top: 0.6em;
  width: 2.8em;
  height: 2.8em;
  border-radius: 0.5em;
  background: #fff;
  margin-right: 0.6em;
  display: block;
}
.ui__notification-text {
  font-size: 0.75em;
  line-height: 1.4em;
}
.ui__notification-text .icon {
  color: #4f82fd;
  font-size: 1.1em;
}
.ui__notification-text b {
  font-weight: 700;
}

.btn--stats {
  visibility: hidden;
}
.btn--autosolve{
  transform: translate3d(0px, 0em, 0px); opacity: 1; pointer-events: all;
}
